package hero.api.messages.service

import hero.api.post.service.PostService
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class MessageCommandServiceIT : IntegrationTest() {
    @Test
    fun `should send a message to a thread`() {
        val now = Instant.ofEpochSecond(1755450161)
        val testClock = TestClock(now)
        val underTest = MessageCommandService(
            TestCollections.messageThreadsCollection,
            PostService(
                postsCollection = TestCollections.postsCollection,
                postRepository = TestRepositories.postRepository,
                gjirafaService = mockk(),
                gjirafaLivestreamService = mockk(),
                pubSub = pubSubMock,
                clock = testClock,
            ),
            testClock,
        )

        testHelper.createUser("filip")
        testHelper.createMessageThread("filip", listOf("pablo"), id = "message-thread-id")

        val message = underTest.execute(SendMessage("filip", "message-thread-id", "text"))

        val storedMessage = TestRepositories.postRepository.getById(message.id)

        assertThat(storedMessage).isEqualTo(message)
        assertThat(storedMessage.text).isEqualTo("text")
        assertThat(storedMessage.userId).isEqualTo("filip")
        assertThat(storedMessage.messageThreadId).isEqualTo("message-thread-id")
        assertThat(storedMessage.published).isEqualTo(now)
        assertThat(storedMessage.assets).isEmpty()
        assertThat(storedMessage.parentId).isNull()
        assertThat(storedMessage.siblingId).isNull()
        assertThat(storedMessage.parentPostId).isNull()
        assertThat(storedMessage.parentUserId).isNull()

        val messageRecord = testContext.selectFrom(Tables.POST).where(Tables.POST.ID.eq(message.id)).fetchSingle()
        assertThat(messageRecord.type).isEqualTo("MESSAGE")

        with(TestCollections.messageThreadsCollection["message-thread-id"].get()) {
            assertThat(lastMessageAt).isEqualTo(now)
            assertThat(lastMessageBy).isEqualTo("filip")
            assertThat(lastMessageId).isEqualTo(message.id)
            assertThat(seens).containsEntry("filip", now)
            assertThat(checks).containsEntry("filip", now)
        }
    }
}
