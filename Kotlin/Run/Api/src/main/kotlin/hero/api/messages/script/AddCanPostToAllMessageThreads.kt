package hero.api.messages.script

import hero.baseutils.SystemEnv
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import hero.repository.subscription.canMessage
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, false)
    val messageThreadsCollection = firestore.typedCollectionOf(MessageThread)

    var lastId: String? = null
    var processed = 0

    do {
        val messageThreads = messageThreadsCollection
            .where(MessageThread::id).isNotEqualTo("")
            .orderBy(MessageThread::id, Sort.Direction.DESC)
            .limit(200)
            .startAfterIfNotNull(lastId)
            .fetchAll()

        if (messageThreads.isEmpty()) {
            break
        }

        runBlocking(Dispatchers.Default) {
            messageThreads.map {
                val userOne = it.userIds.first()
                val userTwo = it.userIds.firstOrNull { it != userOne }
                async {
                    if (userTwo == null) {
                        messageThreadsCollection[it.id].field(MessageThread::canMessage).update(mapOf(userOne to true))
                    } else {
                        val canMessage = canMessage(context, userOne, userTwo)
                        messageThreadsCollection[it.id]
                            .field(MessageThread::canMessage)
                            .update(it.userIds.distinct().associate { it to canMessage })
                    }
                }
            }.awaitAll()
        }

        processed += messageThreads.size
        lastId = messageThreads.last().id
        println("Processed $processed message threads, last $lastId")
    } while (true)
}
